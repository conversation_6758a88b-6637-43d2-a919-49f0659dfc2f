import { Component, <PERSON><PERSON><PERSON> } from '@angular/core';
import { ResultType, SyncResult, UnviredCordovaSDK, UnviredCredential, UnviredResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON><PERSON>roller, LoadingController, ModalController } from '@ionic/angular';
import { TASK_LIST_HEADER } from '../Constants/HEADER';
import { DataService } from '../services/data.service';
import * as moment from 'moment';
import { DownloadTaskPage } from '../download-task/download-task.page';
import { NavigationExtras, Router } from '@angular/router';
import * as jquery from 'jquery';
import { Table } from 'primeng/table';
// import {HttpClient} from '@angular/common/http';
@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage {
  public selectedDate: string;
  public dates: any[] = [];
  public reasonCodeList = [];
  public reasonCode: string;
  public taskList: TASK_LIST_HEADER[] = [];
  public markComplete: string;
  public originalTaskList: any[];
  public inCompleteReason: string = "";
  public errorMessage: string = "";
  public shiftType: string = "All"
  public cp: number = 1;
  public itemsPerPage: number = 8;
  isSearch: boolean = false;
  flocSearch: string = "";
  public funLocList = [];
  queryLineNum: string;
  queryPlantNum: string;
  public isMarkComplete: boolean = false;
  public isMarkInComplete: boolean = false;
  public isTaskDataLoaded: boolean = false;
  public isShowAllTasks: boolean = false;
  public taskListItemsFromDB = [];
  public userFullname = [];
  public datesCounts: any[] = [];
  public newHoverText: string = "Completed"
  public othersCount: any;
  public jsonData: any;

  constructor(
    public alertController: AlertController,
    public dataService: DataService,
    public unviredSdk: UnviredCordovaSDK,
    public loadingController: LoadingController,
    private modalController: ModalController,
    public router: Router,
    public ngZone: NgZone,
    // private http: HttpClient
  ) {
  }


  statuses: any[];
  state: any[];
  async ngOnInit() {

    // this.http.get('assets/data.json').subscribe(data =>{  
    //   this.jsonData = data;   
    //   console.log(this.jsonData);
    // });

    this.statuses = [
      { label: 'Completed', value: 'X' },
      { label: 'Not Completed', value: '' }
    ]

    this.state = [
      { label: 'RUN', value: 'X' },
      { label: 'STOP', value: 'Y' }
    ]

    console.log("Home - ngOnInit called")
    let data = localStorage.getItem('querydata')
    if (data) {
      let querydata = JSON.parse(data);
      this.queryLineNum = querydata.LineNumber;
      this.queryPlantNum = querydata.PlantNumber;
    }

    let that = this;
    jquery(document).ready(function () {
      jquery('.funloc-select').select2();
      jquery('.funloc-select').select2({ width: '100%' });

      jquery('.funloc-select').select2({
        placeholder: "Search for functional location",
        allowClear: true
      });

      var isClearClicked = false;
      jquery('.funloc-select').on('select2:unselecting', function (e) {
        window['isClearClicked'] = true;
        // that.flocSearch = jquery(this).val();
        // that.searchTasks(jquery(this).val());
        that.flocSearch = "";
        that.searchTasks("");
      });

      jquery('.funloc-select').on('select2:opening', function (e) {
        if (window['isClearClicked']) {
          e.preventDefault();
          window['isClearClicked'] = false;
        }
      }).on('select2:unselecting', function (e) {
        window['isClearClicked'] = true;
      });


      jquery('.funloc-select').on("select2:select", function () {
        console.log(jquery(this).val());
        that.flocSearch = jquery(this).val();
        that.searchTasks(jquery(this).val());
      });

      // Shift Field
      jquery('.shift-select').select2();
      jquery('.shift-select').select2({ width: '100%' });
      that.shiftType = 'All'
      jquery('.shift-select').on("select2:select", function () {
        console.log(jquery(this).val());
        that.shiftType = jquery(this).val();
        that.selectedShift();
      });

      jquery('.shift-select').select2({
        placeholder: "Select",
        allowClear: true
      });

      var isClearClickedOfShift = false;
      jquery('.shift-select').on('select2:unselecting', function (e) {
        window['isClearClickedOfShift'] = true;
        that.shiftType = 'All'
        that.selectedShift();
      });

      jquery('.shift-select').on('select2:opening', function (e) {
        if (window['isClearClickedOfShift']) {
          e.preventDefault();
          window['isClearClickedOfShift'] = false;
        }
      }).on('select2:unselecting', function (e) {
        window['isClearClickedOfShift'] = true;
      });
    });

  }

  async loadData() {
    await this.pleaseWaitLoader();
    try {
      let reasonHeaderData = await this.dataService.getData('REASON_HEADER');
      if (reasonHeaderData.length > 0) {
        this.reasonCodeList = reasonHeaderData;
      } else {
        console.log("no data found");
      }

      // let taskListdata = await this.dataService.getData('TASK_LIST_ITEMS');
      // let filteredData = this.jsonData.filter(item => item.COMPLETION_STATUS === '');
      let data = await this.dataService.getData('TASK_LIST_ITEMS');
      console.log("Data before filter", data);
      let filteredData = data.filter(item => item.COMPLETION_STATUS == '' || item.COMPLETION_STATUS == null);
      let taskListdata = filteredData;

      if (taskListdata.length > 0) {
        this.funLocList = [];
        for (let i = 0; i < taskListdata.length; i++) {
          taskListdata[i].isCustomMark = false;
          taskListdata[i].isChanged = false;
          taskListdata[i].errMsg = "";
          taskListdata[i].completion = taskListdata[i].COMPLETION_STATUS;

          if (taskListdata[i].REASON && taskListdata[i].REASON.length > 0 && taskListdata[i].COMPLETION_STATUS !== 'X') {
            taskListdata[i].COMPLETION_STATUS = 'false';
          }
          if (taskListdata[i].COMPLETION_STATUS === 'X') {
            taskListdata[i].COMPLETION_STATUS = 'true'
          }
          if (taskListdata[i].REASON === null) {
            taskListdata[i].REASON = ""
          }
          taskListdata[i].sel = taskListdata[i].REASON;
          let Obj = { FLOC: taskListdata[i].FLOC, FLOC_DESC: taskListdata[i].FLOC_DESC }
          this.funLocList.push(Obj);
          this.funLocList = this.removeDuplicateObjectFromArray(this.funLocList, 'FLOC')
        }
      } else {
        console.log("no data found");
      }

      this.isTaskDataLoaded = true;
      this.taskList = taskListdata;
      this.originalTaskList = this.taskList;
      console.log("original List of tasks", this.originalTaskList);
      // this.taskListItemsFromDB = this.taskList;
      const format = "MMM Do YYYY"
      this.selectedDate = moment().format('MMM Do YYYY');
      let seldateTasks = this.taskList.filter(
        (item) =>
          ((moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate) && item.COMPLETION_STATUS !== 'X' || item.COMPLETION_STATUS == null)
        // ((moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate) && item.COMPLETION_STATUS !== 'X')
      );
      this.taskList = seldateTasks;
      console.log("Tasklist", this.taskList);
      this.taskList = this.taskList.map(tl => {
        if (tl.SYSTEM_CONDITION == null || tl.SYSTEM_CONDITION.length == 0) {
          tl.SYSTEM_CONDITION = "Y"
        }
        return tl
      });

      // await this.unviredSdk.dbExportWebData();
    } finally {
      await this.dismissLoadingController();
    }
  }

  async userLable() {
    let user = await this.dataService.getData('USER_HEADER');
    if (user.length > 0) {
      this.userFullname = user;
    } else {
      console.log("no data found");
    }
  }


  removeDuplicateObjectFromArray(array, key) {
    var check = new Set();
    return array.filter(obj => !check.has(obj[key]) && check.add(obj[key]));
  }

  async ionViewWillEnter() {
    setTimeout(async () => {

      this.errorMessage = '';
      console.log("Home - ionViewWillEnter called")
      this.dataService.resetIdleWatcher()
      await this.pleaseWaitLoader();
      try {
        this.dates = [];
        this.dates.push({ date: moment().subtract(4, 'days').format('MMM Do YYYY') });
        this.dates.push({ date: moment().subtract(3, 'days').format('MMM Do YYYY') });
        this.dates.push({ date: moment().subtract(2, 'days').format('MMM Do YYYY') });
        this.dates.push({ date: moment().subtract(1, 'days').format('MMM Do YYYY') });
        this.dates.push({ date: moment().format('MMM Do YYYY') });
        // this.dates.push({ date: 'Others'});

        // this.date
        console.log("dates list", this.dates);

        this.selectedDate = moment().format('MMM Do YYYY');
        let type = this.dataService.checkStringAndConvertIntoNumber(localStorage.getItem('resultType'));


        // if (type === LoginListenerType.login_success) {
        // Check is credentials are already set or need to set.
        let isCredentialsRequriedResult: UnviredResult =
          await this.unviredSdk.isClientCredentialsSet();
        console.log("isCredentialsRequriedResult = " + isCredentialsRequriedResult)
        if (
          isCredentialsRequriedResult &&
          isCredentialsRequriedResult.data === false
        ) {
          let tempDetails = localStorage.getItem('userDetails');

          if (tempDetails != null && tempDetails != null) {
            let userDetails = JSON.parse(atob(tempDetails));
            await this.unviredSdk.setClientCredentials(userDetails)
          }
        }

        if (!globalThis.loginFlag) {
          await this.getTasksListFromServer();
        }

        // Check if userDetails is null or empty
        // let userDetails = localStorage.getItem('userDetails');
        // if (!userDetails || userDetails.trim() === '') {
        //   await this.logout();
        // } else {
        //   await this.dataService.getMasterData();
        // }
        
        await this.loadData();
        this.getCount();
        this.userLable();


        // }
      } catch (error) {
        console.log("error" + error);
      } finally {
        await this.dismissLoadingController();
      }
    }, 1000);

  }

  getCount() {
    this.datesCounts = [];
    const format = "MMM Do YYYY"
    let counts: any[] = [];
    this.dates.forEach(element => {
      let counter = 0;
      for (let i = 0; i < this.originalTaskList.length; i++) {
        let formattedDate = moment.utc(this.originalTaskList[i].BASIC_START_DATE, 'YYYY-MM-DD').format(format);
        // console.log("formated date", formattedDate);
        if (element.date.toString() == formattedDate.toString()
        ) {
          counter++;
        }

      }
      this.datesCounts.push({ date: element.date, count: counter });
      // this.datesCounts[element.date] = counter
      counts.push(counter);
    });
    this.othersCount = 0;
    for (let i = 0; i < counts.length; i++) {
      this.othersCount += counts[i];
    }
    counts.pop();
    this.datesCounts.push({ date: 'Others', count: this.originalTaskList.length - this.othersCount });
    // counts.push(this.originalTaskList.length - othersCount);
    // this.datesCounts["Others"] = this.originalTaskList.length - this.othersCount
    console.log("1st count", this.datesCounts);
  }

  async getTasksListFromServer() {
    await this.pleaseWaitLoader();
    try {
      let data = localStorage.getItem('querydata')
      if (data) {
        let querydata = JSON.parse(data)
        this.queryLineNum = querydata.LineNumber;
        this.queryPlantNum = querydata.PlantNumber;
      }

      let inputData = { LineNumber: this.queryLineNum, PlantNumber: this.queryPlantNum };
      let taskListres: any = await this.dataService.getTaskList(inputData);
      if (taskListres.type === ResultType.success) {
        // Task list fetched successfully
      } else {
        if (taskListres.message) {
          this.errorMessage = taskListres.message;
          await this.presentErrorAlert(taskListres.message, 'Error');
        }
      }
    } finally {
      await this.dismissLoadingController();
    }
  }

  selectedTab(date) {
    this.selectedDate = date.date;
  }

  segmentChanged(event: any) {
    this.isSearch = false;
    // this.flocSearch = "";
    // jquery(".funloc-select").select2("val", "").trigger("change")
    // jquery(".funloc-select").val('').trigger('change');
    // this.shiftType = "All";
    // jquery(".shift-select").select2("val", "All");
    this.isMarkComplete = false;
    this.isMarkInComplete = false;
    const format = "MMM Do YYYY";
    this.cp = 1;
    this.selectedDate = event.detail.value;
    this.searchTasks(this.flocSearch);

    // if (event.detail.value == 'Others') {
    //   if (this.originalTaskList) {
    //     var selDate = moment(new Date(moment().subtract(5, 'days').format('YYYY-MM-DD')));
    //     let seldateTasks = this.originalTaskList.filter(
    //       (item) =>
    //         // moment(item.BASIC_START_DATE) < selDate && this.shiftType === item.SHIFT.toLowerCase()
    //         moment(item.BASIC_START_DATE) <= selDate

    //     );
    //     seldateTasks = seldateTasks.sort(function (a, b) { return (a.BASIC_START_DATE > b.BASIC_START_DATE) ? 1 : ((a.BASIC_START_DATE < b.BASIC_START_DATE) ? -1 : 0); });

    //     this.taskList = seldateTasks;
    //     this.markCompleteTasks();
    //   }
    // } else {
    //   if (this.originalTaskList) {
    //     let seldateTasks = this.originalTaskList.filter(
    //       (item) =>
    //         // moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate && this.shiftType === item.SHIFT.toLowerCase()
    //         moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate

    //     );
    //     this.taskList = seldateTasks;
    //     this.markCompleteTasks();
    //   }
    // }
  }

  logout() {
    this.confirmationDialog('Are you sure you want to logout?');
  }

  async confirmationDialog(msg: string) {
    const alert = await this.alertController.create({
      header: 'Confirmation',
      mode: 'ios',
      cssClass: 'secondary',
      message: `<strong>${msg}</strong>`,
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          cssClass: 'secondary',
          handler: async () => {
            this.dataService.logout();
          },
        },
      ],
    });
    await alert.present();
  }


  async updateTaskList() {
    await this.pleaseWaitLoader();
    try {
      let updateTasklists = this.taskList;
      let flag = false;

      for (let i = 0; i < updateTasklists.length; i++) {
        if (updateTasklists[i].COMPLETION_STATUS === 'false' && !updateTasklists[i].REASON) {
          flag = true;
          break;
        }
      }

      updateTasklists.forEach(async (item) => {
        if (item.COMPLETION_STATUS === 'true') {
          item.COMPLETION_STATUS = 'X';
          // item.REASON = "";
        } else {
          item.COMPLETION_STATUS = "";
          // item.REASON = "";
        }
        item.errMsg = "";
      });

      let taskListItems = updateTasklists.filter(
        (item) =>
          item.isChanged == true && item.selReason !== item.REASON && item.COMPLETION_STATUS !== item.completion
      );
      // let taskListItems = this.originalTaskList.filter(originalTask => this.taskList.some(task => {((originalTask.OPERATION_NO === task.OPERATION_NO) && (originalTask.REASON !== task.REASON))}))
      // let taskListItems = this.originalTaskList.filter(task => this.taskList.some(
      //   originalTask => 
      //     (originalTask.OPERATION_NO === task.OPERATION_NO && originalTask.REASON !== task.REASON)
      //   ));
      // console.log(taskListItems);
      // let taskListItems = this.taskList;

      //   const filterByReference = (arr1, arr2) => {
      //     let res = [];
      //     res = arr1.filter(el => {
      //        return arr2.find(element => {
      //           return (element.OPERATION_NO === el.OPERATION_NO && element.REASON !== el.REASON);
      //        });
      //     });
      //     return res;
      //  }
      //  let taskListItems = filterByReference(this.taskList, this.taskListItemsFromDB);
      console.log("taskListItems ÷" + taskListItems)

      // for (let i = 0; i < this.taskList.length; i++) {
      //   if (this.taskList[i].COMPLETION_STATUS === 'false' && !this.taskList[i].REASON) {
      //     flag = true;
      //     break;
      //   }
      // }


      if (flag) {
        await this.presentErrorAlert('Please Provide Reason to Marked Incompleted tasks.', 'Error');
        return;
      }

      // taskListItems.forEach(async (item) => {
      //   if (item.COMPLETION_STATUS === 'true') {
      //     item.COMPLETION_STATUS = 'X';
      //     item.REASON = "";
      //   } else {
      //     item.COMPLETION_STATUS = "";
      //     // item.REASON = "";
      //   }
      //   item.errMsg = "";
      // });

      let data = localStorage.getItem('querydata')
      if (data) {
        let querydata = JSON.parse(data);
        this.queryLineNum = querydata.LineNumber;
        this.queryPlantNum = querydata.PlantNumber;
      }
      let customInput = {

        TASK_LIST: [
          {
            TASK_LIST_HEADER: {
              ORDER_TYPE: "CILT",
              LINE: this.queryLineNum,
              STR_NO: "",
              PLANT: this.queryPlantNum

            },
            TASK_LIST_ITEMS: taskListItems
          }
        ]
      }


      let res: any = await this.dataService.upadteTaskList(
        customInput
      );

      if (res.type == 0) {
        // let taskListdata = await this.dataService.getData('TASK_LIST_ITEMS');
        // if (taskListdata.length > 0) {
        //   for (let i = 0; i < taskListdata.length; i++) {
        //     taskListdata[i].isCustomMark = false;
        //     taskListdata[i].isChanged = false;
        //     taskListdata[i].errMsg = "";
        //   }
        // } else {
        //   console.log("no data found");
        // }
        let toastMsg = "Task updated successfully";

        if (res !== undefined) {

          if (res.data && res.data.TASK_LIST && res.data.TASK_LIST.length > 0 && res.data.TASK_LIST[0].TASK_LIST_ITEMS) {
            let listItems = res.data.TASK_LIST[0].TASK_LIST_ITEMS
            toastMsg = listItems.length + ' ' + toastMsg;
            // for (let i = 0; i < listItems.length; i++) {
            //   listItems[i].isCustomMark = false;
            //   listItems[i].isChanged = false;
            //   listItems[i].errMsg = "";
            //   listItems[i].selReason = listItems[i].REASON;
            // }
            this.cp = 1;
            // for (let i = 0; i < this.taskList.length; i++) {
            //   listItems.forEach((item) => {
            //     if (this.taskList[i].OPERATION_NO === item.OPERATION_NO) {
            //       this.taskList[i] = item;
            //     }
            //     if(this.taskList[i].REASON && this.taskList[i].REASON.length > 0 && this.taskList[i].COMPLETION_STATUS !== 'X'){
            //       this.taskList[i].COMPLETION_STATUS = 'false';
            //      }
            //   });
            // }
          }
          //  this.dataService.displayAlert(toastMsg)
          this.isTaskDataLoaded = true;
          let alertRes = await this.displayUpdateTaskAlertMessage(toastMsg);
          if (alertRes) {
            await this.pleaseWaitLoader();
            await this.getTasksListFromServer();
            await this.loadData();
          }
        }
        // this.dataService.showToast(toastMsg);

        // this.taskList = taskListdata;
        // this.originalTaskList = this.taskList;

        // let seldateTasks = this.taskList.filter(
        //   (item) =>
        //     item.COMPLETION_STATUS !== 'X'
        // );
        // this.taskList = seldateTasks;

      } else {
        let errMsg = 'Error: Not provided valid data. check error below items.'
        if (res.message) {
          this.markComplete = "";
          this.isMarkComplete = false;
          this.isMarkInComplete = false;
          errMsg = res.message;
          let errArr = res.message.split('I:')

          for (let i = 0; i < errArr.length; i++) {
            if (errArr[i] != " ") {
              let errmsg = errArr[i].split(':');
              this.taskList.forEach((item) => {
                if (errmsg[0] === item.ORDERID && errmsg[1] === item.OPERATION_NO) {
                  item.errMsg = errmsg[2];
                  item.COMPLETION_STATUS = "";
                  errMsg = 'Error: Not provided valid data. check error below items.'
                }
              });
            }
          }
        }
        await this.presentErrorAlert(errMsg, 'Alert');
      }
    } finally {
      await this.dismissLoadingController();
    }
  }
  cancel() {
    this.confirmationCancelDialog('Unsaved changes will be lost. Continue?');
  }

  async confirmationCancelDialog(msg: string) {
    const alert = await this.alertController.create({
      header: 'Warning',
      mode: 'ios',
      cssClass: 'secondary',
      message: `<strong>${msg}</strong>`,
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          cssClass: 'secondary',
          handler: async () => {
            await this.loadData();
            this.markComplete = "";
            this.isMarkComplete = false;
            this.isMarkInComplete = false;
          },
        },
      ],
    });
    await alert.present();
  }
  setCompletion(event, item) {
    console.log(item, event.detail.value)
    this.taskList.forEach((item) => {
      if (item.COMPLETION_STATUS === 'true' || item.COMPLETION_STATUS === 'false') {
        item.isChanged = true;
      }
    });
    if (event.detail.value === 'true') {
      item.REASON = "";
    } else if (event.detail.value === 'false') {

    }
  }
  markCompleteTasks() {
    // let initialValue = (this.itemsPerPage * (this.cp - 1));
    //   console.log("initialValue" + initialValue);
    //   let finalValue = (this.itemsPerPage * this.cp);
    //   console.log("finalValue" + finalValue);

    if (this.isMarkComplete) {
      this.taskList.forEach((item) => {
        if ((!item.COMPLETION_STATUS && !item.isCustomMark) || item.isCustomMark) {
          item.COMPLETION_STATUS = 'true';
          item.isCustomMark = true;
        }
      });
      // for(let i = initialValue; i< finalValue; i++){
      //   let item = this.taskList[i];
      //   if ((!item.COMPLETION_STATUS && !item.isCustomMark) || item.isCustomMark) {
      //     item.COMPLETION_STATUS = 'true';
      //     item.isCustomMark = true;
      //   }
      // }
    } else if (this.isMarkInComplete) {
      this.taskList.forEach((item) => {
        if ((!item.COMPLETION_STATUS && !item.isCustomMark) || item.isCustomMark) {
          item.COMPLETION_STATUS = 'false';
          item.isCustomMark = true;

        }
      });

      // for(let i = initialValue; i< finalValue; i++){
      //   let item = this.taskList[i];
      //   if ((!item.COMPLETION_STATUS && !item.isCustomMark) || item.isCustomMark) {
      //     item.COMPLETION_STATUS = 'false';
      //     item.isCustomMark = true;
      //   }
      // }
    } else {
      this.taskList.forEach((item) => {
        if (item.isChanged && item.isCustomMark) {
          item.REASON = "";
          item.COMPLETION_STATUS = "";
          item.isChanged = false;
          item.isCustomMark = false;
          this.inCompleteReason = ""
        }
      });
    }
  }

  async searchTasks(value) {
    if (this.originalTaskList) {
      this.isSearch = true;
      if (value) {
        let seldateTasks = await this.filterItems(value);
        this.taskList = seldateTasks.sort(function (a, b) { return (a.BASIC_START_DATE > b.BASIC_START_DATE) ? 1 : ((a.BASIC_START_DATE < b.BASIC_START_DATE) ? -1 : 0); });

      } else {
        let seldateTasks = await this.filterItems('');
        this.taskList = seldateTasks.sort(function (a, b) { return (a.BASIC_START_DATE > b.BASIC_START_DATE) ? 1 : ((a.BASIC_START_DATE < b.BASIC_START_DATE) ? -1 : 0); });
      }
      this.isSearch = false;
      this.cp = 1;
      this.markCompleteTasks()

    }
  }

  async filterItems(searchTerm: string) {
    const format = "MMM Do YYYY"
    this.originalTaskList = await this.dataService.getData('TASK_LIST_ITEMS');
    if (!searchTerm) {
      if (this.originalTaskList) {
        if (this.selectedDate === 'Others') {
          // var selDate = moment().subtract(4, 'days');
          // var selDate = moment(new Date(moment().subtract(5, 'days').format('YYYY-MM-DD')));
          var selDate = moment(moment().subtract(5, "days").format("YYYY-MM-DD"));
          if (this.shiftType === 'All') {
            return this.originalTaskList.filter(
              (item) => {
                // return (moment(item.BASIC_START_DATE) <= selDate)
                if (!this.isShowAllTasks) {
                  return (moment(item.BASIC_START_DATE) <= selDate && item.COMPLETION_STATUS !== 'X')
                } else {
                  return (moment(item.BASIC_START_DATE) <= selDate)
                }
              });
          } else {
            return this.originalTaskList.filter(
              (item) => {
                // return moment(item.BASIC_START_DATE) <= selDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase())
                if (!this.isShowAllTasks) {
                  return moment(item.BASIC_START_DATE) <= selDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X'
                } else {
                  return moment(item.BASIC_START_DATE) <= selDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase())
                }
              });
          }

        } else {
          if (this.shiftType === 'All') {
            return this.originalTaskList.filter(
              (item) => {
                if (!this.isShowAllTasks) {

                  return moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && item.COMPLETION_STATUS !== 'X'

                  // return moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate && item.COMPLETION_STATUS !== 'X'
                } else {
                  // return moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate
                  return moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate
                }
              });
          } else {
            return this.originalTaskList.filter(
              (item) => {
                if (!this.isShowAllTasks) {
                  return moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X'
                } else {
                  return moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase())
                }
              });
          }
        }
      }

    } else {
      console.log("searching tasks")
      if (this.shiftType === 'All') {
        if (this.selectedDate === 'Others') {
          // var selDate = moment().subtract(4, 'days');
          var selDate = moment(moment().subtract(5, "days").format('YYYY-MM-DD'));
          return this.originalTaskList.filter(
            (item) => {
              // return ((moment(item.BASIC_START_DATE) <= selDate) && (item.FLOC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1) || (item.FLOC_DESC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1))

              if (!this.isShowAllTasks) {
                return ((moment(item.BASIC_START_DATE) <= selDate) && (item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim()) && item.COMPLETION_STATUS !== 'X')

              } else {
                return ((moment(item.BASIC_START_DATE) <= selDate) && (item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim()))
              }
            });
        } else {
          return this.originalTaskList.filter(item => {
            // return ((item.FLOC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1) || (item.FLOC_DESC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1)) && (moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate);
            if (!this.isShowAllTasks) {
              return ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim())) && (moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate) && item.COMPLETION_STATUS !== 'X';
            } else {
              return ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim())) && (moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate);
            }

          });
        }
      } else {
        if (this.selectedDate === 'Others') {
          // var selDate = moment().subtract(4, 'days');
          var selDate = moment(moment().subtract(5, "days").format('YYYY-MM-DD'));
          return this.originalTaskList.filter(
            (item) => {
              // return ((moment(item.BASIC_START_DATE) <= selDate) && ((item.FLOC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1) || (item.FLOC_DESC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1)) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()))
              if (!this.isShowAllTasks) {
                return ((moment(item.BASIC_START_DATE) <= selDate) && ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim())) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X')
              } else {
                return ((moment(item.BASIC_START_DATE) <= selDate) && ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim())) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()))
              }
            });
        } else {
          return this.originalTaskList.filter(item => {
            // return ((item.FLOC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1) || (item.FLOC_DESC.toLowerCase().trim().indexOf(searchTerm.toLowerCase().trim()) > -1)) && (moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase());
            if (!this.isShowAllTasks) {
              return ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim()) && (moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X');
            } else {
              return ((item.FLOC.toLowerCase().trim() === searchTerm.toLowerCase().trim()) && (moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate) && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()));
            }
          });
        }
      }
    }
  }
  selectedReason() {
    this.taskList.forEach((item) => {
      if ((!item.COMPLETION_STATUS && !item.isCustomMark) || item.isCustomMark) {
        item.REASON = this.inCompleteReason;
      }
    });
  }
  selectedShift() {
    // this.isSearch = false;
    // this.flocSearch = "";
    // jquery(".funloc-select").select2("val", "");
    this.cp = 1;
    // this.filterTasks();
    this.searchTasks(this.flocSearch);
  }

  filterTasks() {
    const format = "MMM Do YYYY";
    if (this.selectedDate === 'Others') {
      if (this.originalTaskList) {
        var selDate = moment(moment().subtract(5, "days").format('YYYY-MM-DD'));
        console.log("selDate = " + selDate)
        let seldateTasks;
        if (this.shiftType === 'All') {
          seldateTasks = this.originalTaskList.filter(
            (item) => {
              if (!this.isShowAllTasks) {
                moment(item.BASIC_START_DATE) <= selDate && item.COMPLETION_STATUS !== 'X'
              } else {
                moment(item.BASIC_START_DATE) <= selDate
              }
            });
        } else {
          seldateTasks = this.originalTaskList.filter(
            (item) => {
              if (!this.isShowAllTasks) {
                moment(item.BASIC_START_DATE) <= selDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X'

              } else {
                moment(item.BASIC_START_DATE) <= selDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase())
              }
            });
        }
        seldateTasks = seldateTasks.sort(function (a, b) { return (a.BASIC_START_DATE > b.BASIC_START_DATE) ? 1 : ((a.BASIC_START_DATE < b.BASIC_START_DATE) ? -1 : 0); });

        this.taskList = seldateTasks;
      }
    } else {
      if (this.originalTaskList) {
        let seldateTasks;
        if (this.shiftType === 'All') {
          seldateTasks = this.originalTaskList.filter(
            (item) => {
              if (!this.isShowAllTasks) {
                moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && item.COMPLETION_STATUS !== 'X'
              } else {
                moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate
              }
            });
        } else {
          seldateTasks = this.originalTaskList.filter(
            (item) => {
              if (!this.isShowAllTasks) {
                moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase()) && item.COMPLETION_STATUS !== 'X'
              } else {
                moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate && (item.SHIFT && this.shiftType === item.SHIFT.toLowerCase())
              }
            });
        }
        this.taskList = seldateTasks;
      }
    }
  }
  async download() {
    await this.pleaseWaitLoader();
    try {
      this.isSearch = false;
      this.flocSearch = "";
      jquery(".funloc-select").select2("val", "");
      const modal = await this.modalController.create({
        component: DownloadTaskPage,
        cssClass: 'download-task-modal'

      });
      await modal.present();
      const { data } = await modal.onWillDismiss();
      if (data) {
        this.isMarkComplete = false;
        this.isMarkInComplete = false;
        this.queryLineNum = data.LineNumber;
        this.queryPlantNum = data.PlantNumber;
        let inputData = { LineNumber: data.LineNumber, PlantNumber: data.PlantNumber };
        localStorage.setItem('querydata', JSON.stringify(inputData));

        let taskListres: any = await this.dataService.getTaskList(inputData);
        this.selectedDate = moment().format('MMM Do YYYY');
        if (taskListres.type === ResultType.success) {
          await this.loadData();
        } else {
          if (taskListres.message) {
            this.errorMessage = taskListres.message;
            await this.presentErrorAlert(taskListres.message, 'Error');
            this.resetUI()
          }
        }
      } else {
        console.log("no data received from download task page")
      }
      this.getCount();
    } catch (error) {
      console.log(error);
    } finally {
      await this.dismissLoadingController();
    }
  }
  resetUI() {
    this.originalTaskList = [];
    this.taskList = [];
    this.isSearch = false;
    this.flocSearch = "";
    jquery(".funloc-select").select2("val", "");
    this.cp = 1;
    this.isMarkComplete = false;
    this.isMarkInComplete = false;

  }
  async presentErrorAlert(msg: string, headerMsg: string) {
    if (msg.includes("Incorrect user credentials")) {
      await this.unviredSdk.setClientCredentials([]);
      let queryData = localStorage.getItem('querydata');
      localStorage.clear();
      await this.unviredSdk.logout();
      this.ngZone.run(() => {
        localStorage.setItem('querydata', queryData);
        localStorage.setItem('resultType', '0');
      });

      const alert = await this.alertController.create({
        header: 'Error',
        message: msg,
        backdropDismiss: false,
        buttons: [
          {
            text: 'OK',
            role: 'confirm',
            handler: () => {
              this.ngZone.run(() => this.router.navigate(['login']));
            },
          },
        ],
      });
      await alert.present();
    } else {
      const alert = await this.alertController.create({
        header: headerMsg,
        message: msg,
        backdropDismiss: false,
        mode: 'ios',
        buttons: ['OK']
      });
      await alert.present();
    }
  }

  clickev(ev) {
    console.log("clickev called");
  }

  checkMarkInComplete(event) {
    if (event.detail.checked) {
      this.isMarkComplete = false;
      this.isMarkInComplete = true;
    } else {
      this.isMarkInComplete = false;
    }
    this.markCompleteTasks()
  }
  checkMarkComplete(event) {
    if (event.detail.checked) {
      this.isMarkComplete = true;
      this.isMarkInComplete = false;
      this.inCompleteReason = "";
      this.taskList.forEach(async (item) => {
        if (item.isCustomMark) {
          item.REASON = "";
        }
      });
    } else {
      this.isMarkComplete = false;
    }
    this.markCompleteTasks()
  }
  async showAllTasks(event) {
    const format = "MMM Do YYYY"
    this.cp = 1;
    this.flocSearch = "";
    jquery(".funloc-select").select2("val", "");
    if (event.detail.checked) {
      this.isShowAllTasks = true;
      // let seldateTasks = this.originalTaskList.filter(
      //   (item) =>
      //   moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate
      // );
      // this.taskList = seldateTasks;
    } else {
      this.isShowAllTasks = false;
      // let seldateTasks = this.originalTaskList.filter(
      //   (item) =>
      //     ((moment(new Date(item.BASIC_START_DATE)).format(format) === this.selectedDate) && item.COMPLETION_STATUS !== 'X')
      // );
      // this.taskList = seldateTasks;
    }
    let seldateTasks = await this.filterItems('');
    this.taskList = seldateTasks.sort(function (a, b) { return (a.BASIC_START_DATE > b.BASIC_START_DATE) ? 1 : ((a.BASIC_START_DATE < b.BASIC_START_DATE) ? -1 : 0); });
    for (let i = 0; i < this.taskList.length; i++) {
      if (this.taskList[i].REASON && this.taskList[i].REASON.length > 0 && this.taskList[i].COMPLETION_STATUS !== 'X') {
        this.taskList[i].COMPLETION_STATUS = 'false';
      }
    }
  }


  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  async displayUpdateTaskAlertMessage(msg: string): Promise<boolean> {
    let resolveFunction: (confirm: boolean) => void;
    let promise = new Promise<boolean>(resolve => {
      resolveFunction = resolve;
    });

    const alert = await this.alertController.create({
      header: 'Confirmation',
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: 'Ok',
          role: 'cancel',
          handler: () => {
            resolveFunction(true);
          }
        }
      ]
    });
    await alert.present();
    return promise;
  }
  async ionViewDidLeave() {
    console.log("ionViewDidLeave called")
    await this.dismissLoadingController();
  }

  clear(table: Table) {
    table.clear();
  }
}