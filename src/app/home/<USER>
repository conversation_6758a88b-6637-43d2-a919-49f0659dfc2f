<ion-header [translucent]="true">
  <ion-toolbar class="toolbar-bg">
    <div class="header-title">
      <ion-title>
        <span style="vertical-align: -webkit-baseline-middle !important;">CILT Task Management</span>
      </ion-title>
      <ion-title class="text-center">
        <span *ngFor="let users of userFullname" class="user-title">{{users.FULLNAME | titlecase }} - Logged In</span>
      </ion-title>
    </div>
    <ion-buttons slot="end" class="download-button">
      <ion-button (click)="download()">Switch Line
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end" class="download-button">
      <ion-button (click)="logout()">LOGOUT
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-header>
  <ion-toolbar>

    <!-- <ion-title style="width: 60%;">
     <span>CILT(s) for line {{queryLineNum}} and Plant {{queryPlantNum}}</span>
    </ion-title> -->

    <!-- <ion-buttons slot="end" class="download-button">
   <ion-button (click)="download()">Download
    </ion-button>
 </ion-buttons> -->

    <ion-grid>
      <ion-row>
        <ion-col size="7">
          <span style="margin-left: 8px;font-weight: bold;">CILT(s) for line {{queryLineNum}} and Plant
            {{queryPlantNum}}</span>
        </ion-col>

        <!-- Show All tasks -->
        <!-- <ion-col size="1" style="text-align: right;">
    <div style="display: flex;margin: 10px;" *ngIf="taskList && taskList.length > 0">
      <ion-checkbox color="primary" (ionChange)="showAllTasks($event)" [(ngModel)]="isShowAllTasks"></ion-checkbox>
      <ion-label style="margin-left: 10px;">Show All Tasks.</ion-label>
    </div>
</ion-col> -->

        <ion-col size="5" style="justify-content: flex-end;display: inline-flex;padding-right: 10px;">
          <!-- <ion-label>Shift</ion-label> -->
          <!-- <div style="display: flex;    margin-top: 12px;margin-right: 12px;" *ngIf="(originalTaskList && originalTaskList.length > 0)"> -->
          <!-- <ion-checkbox color="primary" (ionChange)="showAllTasks($event)" [(ngModel)]="isShowAllTasks"></ion-checkbox> -->
          <!-- <ion-label style="margin-left: 10px;">Show All Tasks.</ion-label> -->
          <ion-item lines="none" *ngIf="(originalTaskList && originalTaskList.length > 0)">
            <ion-toggle style="margin-bottom: 5px;" [(ngModel)]="isShowAllTasks" (ionChange)="showAllTasks($event)"
              mode="md">
            </ion-toggle>
            <ion-label style="margin-bottom: 15px;">Show All Tasks.</ion-label>
          </ion-item>

          <!-- </div> -->
          <span style="font-weight: bold;margin-top: 12px;">Shift</span>&nbsp;
          <select class="shift-select"
            [disabled]="((taskList && taskList.length === 0 && flocSearch.length === 0) || isSearch) && shiftType === ''"
            [(ngModel)]="shiftType"
            style="border-radius: 5px;width: 40% !important;color: var(--ion-text-color) !important;">
            <option value="day">Day</option>
            <option value="night">Night</option>
          </select>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-toolbar>
</ion-header>

<ion-header class="background-color">
  <ion-toolbar>
    <ion-grid>
      <ion-row>
        <ion-col size="8">
          <ion-segment class="background-color" mode="ios" (ionChange)="segmentChanged($event)" style="margin: 5px"
            value="{{selectedDate}}">
            <ion-segment-button (click)="clear(taskTable)" class="bd-left" *ngFor="let date of datesCounts"
              [ngClass]="selectedDate === date.date ? 'segment-active': 'segment-deactive'" value="{{date.date}}">
              <ion-label>{{date.date}}</ion-label>
              <div class="notification-dot">{{ date.count }}</div>
            </ion-segment-button>
          </ion-segment>
        </ion-col>
        <ion-col size="4" style="justify-content: flex-end;display: inline-flex;margin-top: 10px !important;">
          <select class="funloc-select" name="state"
            [disabled]="(taskList && taskList.length === 0 && flocSearch.length === 0 ) || isSearch "
            style="width: 100% !important; text-align: start; --background: white;border-radius: 5px;color: var(--ion-text-color) !important;;height: 45px !important;margin-top: 10px !important;"
            slot="end" (change)="searchTasks($event)" [(ngModel)]="flocSearch">
            <option disabled value="" selected>Search for Functional Location </option>
            <option *ngFor="let item of funLocList" value="{{item.FLOC}}">
              {{item.FLOC}} - {{item.FLOC_DESC}}</option>
          </select>
        </ion-col>
        <!-- 
    <ion-searchbar *ngIf="taskList && taskList.length > 0  || isSearch"
      style="width: 35%; text-align: start; --background: white;border-radius: 10px;color: black;" slot="end"
      placeholder="Search for functional location" animated="true" (ionInput)="searchTasks($event)" #searchbar
      [(ngModel)]="flocSearch"></ion-searchbar> -->

      </ion-row>
    </ion-grid>
  </ion-toolbar>
</ion-header>

<ion-content>

  <p-table #taskTable [value]="taskList" [paginator]="true" [rows]="50" [showCurrentPageReport]="true"
    responsiveLayout="scroll" [paginatorPosition]="'top'" [scrollHeight]="'100%'" [rowsPerPageOptions]="[5, 10, 20, 50]"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [globalFilterFields]="['TASK_TYPE','OPERATION_NO','OPERATION_DESC','SYSTEM_CONDITION','BASIC_FINISH_DATE']">

    <ng-template pTemplate="caption">
      <button pButton label="Clear" class="p-button-outlined" icon="pi pi-filter-slash"
        (click)="clear(taskTable)"></button>

      <span class="p-input-icon-left search-float-right">
        <i class="pi pi-search"></i>
        <input pInputText type="text" (input)="taskTable.filterGlobal($any($event.target).value, 'contains')"
          placeholder="Search" />
      </span>
    </ng-template>
    <ng-template pTemplate="header">
      <tr>
        <th pSortableColumn="TASK_TYPE">Type<p-sortIcon field="TASK_TYPE"></p-sortIcon></th>
        <th pSortableColumn="OPERATION_NO">Task Number<p-sortIcon field="OPERATION_NO"></p-sortIcon></th>
        <th pSortableColumn="OPERATION_DESC">Task<p-sortIcon field="OPERATION_DESC"></p-sortIcon></th>
        <th>State</th>
        <th pSortableColumn="SHIFT">Shift<p-sortIcon field="SHIFT"></p-sortIcon></th>
        <th>Status</th>
        <th pSortableColumn="REASON">Reason<p-sortIcon field="REASON"></p-sortIcon></th>
        <th pSortableColumn="OPERATION_NO">Opl<p-sortIcon field="OPERATION_NO"></p-sortIcon></th>
        <th pSortableColumn="BASIC_FINISH_DATE">Finish Date<p-sortIcon field="BASIC_FINISH_DATE"></p-sortIcon></th>
      </tr>
      <tr>
        <th><p-columnFilter type="text" field="TASK_TYPE" display="row" placeholder="Type Here..."></p-columnFilter>
        </th>

        <th><p-columnFilter type="text" field="OPERATION_NO" display="row" placeholder="Type Here..."></p-columnFilter>
        </th>

        <th><p-columnFilter type="text" field="OPERATION_DESC" display="row"
            placeholder="Type Here..."></p-columnFilter></th>

        <th><p-columnFilter class="filter-dropdown" field="SYSTEM_CONDITION" matchMode="equals" display="row"
            class="ml-auto">
            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
              <p-dropdown [ngModel]="value" [options]="state" (onChange)="filter($event.value)" placeholder="Any">
                <ng-template let-option pTemplate="item">
                  <span>{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </ng-template>
          </p-columnFilter>
        </th>

        <th>
          <p-columnFilter type="text" field="SHIFT" display="row" placeholder="Type Here..."></p-columnFilter>
        </th>

        <th><p-columnFilter field="COMPLETION_STATUS" matchMode="equals" display="row" class="ml-auto">
            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
              <p-dropdown [ngModel]="value" [options]="statuses" (onChange)="filter($event.value)" placeholder="Any">
                <ng-template let-option pTemplate="item">
                  <span>{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </ng-template>
          </p-columnFilter>
        </th>

        <th>
          <p-columnFilter type="text" field="REASON" display="row" placeholder="Type Here..."></p-columnFilter>
        </th>

        <th>
          <p-columnFilter type="text" field="OPERATION_NO" display="row" placeholder="Type Here..."></p-columnFilter>
        </th>
        <th>
          <p-columnFilter type="text" field="BASIC_FINISH_DATE" display="row"
            placeholder="Type Here..."></p-columnFilter>
        </th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-task>
      <tr>
        <td><ion-icon name="hand-right" color="warning"
            *ngIf="task && task.TASK_TYPE && task.TASK_TYPE.includes('Clean')">
          </ion-icon>
          <ion-icon name="eye" color="primary" *ngIf="task && task.TASK_TYPE && task.TASK_TYPE.includes('Inspect')">
          </ion-icon>
          <ion-icon name="color-fill" color="danger" *ngIf="task && task.TASK_TYPE && task.TASK_TYPE.includes('Lube')">
          </ion-icon>
          <ion-icon name="build" color="danger" *ngIf="task && task.TASK_TYPE && task.TASK_TYPE.includes('Tighten')">
          </ion-icon>
        </td>

        <td>{{task.OPERATION_NO}}</td>

        <td>{{task.OPERATION_DESC}}</td>

        <td styleClass="custom-chip-run">
          <button *ngIf="task.SYSTEM_CONDITION == 'X'" pButton type="button" label="RUN" disabled="true"
            class="p-button-success"></button>
          <button *ngIf="task.SYSTEM_CONDITION == 'Y'" pButton type="button" label="STOP" disabled="true"
            class="p-button-danger"></button>
        </td>

        <!-- <td *ngIf="task.SYSTEM_CONDITION != ''" styleClass="custom-chip-stop"><button pButton type="button"
            disabled="true" label="STOP" class="p-button-danger"></button></td> -->

        <td>{{task.SHIFT}}</td>

        <td>
          <ion-col size="2"
            [ngClass]="{ 'row-priority': (task.PRIORITY === 'X' && task.COMPLETION_STATUS !== 'X'), 'row-disable': task.COMPLETION_STATUS === 'X', 'row-enable': task.COMPLETION_STATUS !== 'X' }">
            <ion-radio-group style="display: flex; align-items: center;" class="radio-button-margin"
              [(ngModel)]="task.COMPLETION_STATUS" *ngIf="task.COMPLETION_STATUS !== 'X'"
              (ionChange)="setCompletion($event, task)">
              <ion-radio mode="md" slot="start" color="primary" value="true" (ionSelect)="clickev($event)"></ion-radio>
              <div title="Completed">
                <ion-icon name="checkmark-circle" color="success"></ion-icon>
              </div>
              <ion-radio mode="md" slot="start" color="primary" value="false"></ion-radio>
              <div title="Not Completed">
                <ion-icon name="close-circle" color="danger"></ion-icon>
              </div>
            </ion-radio-group>

            <ion-radio-group style="display: flex; align-items: center;" [(ngModel)]="task.COMPLETION_STATUS"
              *ngIf="task.COMPLETION_STATUS === 'X' && task.REASON === ''" value="{{task.COMPLETION_STATUS}}">
              <ion-radio mode="md" slot="start" value="{{task.COMPLETION_STATUS}}" color="primary">
              </ion-radio>
              <div title="Completed">
                <ion-icon name="checkmark-circle" color="success"></ion-icon>
              </div>
            </ion-radio-group>

            <ion-radio-group style="display: flex; align-items: center;" [(ngModel)]="task.COMPLETION_STATUS"
              value="{{task.COMPLETION_STATUS}}"
              *ngIf="task.COMPLETION_STATUS === 'X' && task.REASON && task.REASON.length > 0">
              <ion-radio mode="md" slot="start" color="primary" value="{{task.COMPLETION_STATUS}}"></ion-radio>
              <div title="Not Completed">
                <ion-icon name="close-circle" color="danger"></ion-icon>
              </div>
            </ion-radio-group>
          </ion-col>
        </td>

        <td>
          <ion-col size="2"
            [ngClass]="{ 'row-priority': (task.PRIORITY === 'X' && task.COMPLETION_STATUS !== 'X'), 'row-disable': task.COMPLETION_STATUS === 'X', 'row-enable': task.COMPLETION_STATUS !== 'X' }">
            <select style="border-radius: 5px;height: 70%;" [ngClass]="{'row-disable': task.COMPLETION_STATUS === 'X'}"
              class="text-color" [(ngModel)]="task.REASON"
              *ngIf="task.COMPLETION_STATUS === 'false' || (task.REASON && task.REASON.length > 0)" required
              value="{{task.REASON}}">
              <option disabled value="" selected>Select</option>
              <option *ngFor="let reason of reasonCodeList" value="{{reason.CODE}}">
                {{reason.DESCRP}}</option>
            </select>
          </ion-col>
        </td>

        <td>
          <div *ngIf="task.URL">
            <a href="{{task.URL}}" target="_blank">{{task.OPERATION_NO}}</a>
          </div>
        </td>
        <td>{{task.BASIC_FINISH_DATE}}</td>
      </tr>
    </ng-template>
  </p-table>

  <!-- Old table code -->
  <!-- <ion-radio slot="start" color="primary" value=""></ion-radio>
  <ion-card class="header-card" *ngIf="taskList && taskList.length > 0">
    <ion-grid style="margin-bottom: -10px !important;margin-top: 5px;">
      <ion-row>
        <ion-col size="1">
          <p class="header-align">Type</p>
        </ion-col>

        <ion-col size="1">
          <p class="">Task Number</p>
        </ion-col>

        <ion-col size="3" *ngIf="selectedDate !== 'Others'">
          <p class="header-align">Task</p>
        </ion-col>

        <ion-col size="2" *ngIf="selectedDate === 'Others'">
          <p class="header-align">Task</p>
        </ion-col>

        <ion-col size="1.25" *ngIf="selectedDate === 'Others'">
          <p class="header-align">Date</p>
        </ion-col>

        <ion-col size="0.75">
          <p class="header-align">State</p>
        </ion-col>

        <ion-col size="1" style="text-align: center;">
          <p>Shift</p>
        </ion-col>

        <ion-col size="2">
          <p class="header-align">Status</p>
        </ion-col>

        <ion-col size="2">
          <p class="header-align">Reason</p>
        </ion-col>
        <ion-col size="1">
          <p class="header-align">Opl</p>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card>

  <div *ngIf="taskList && taskList.length === 0 && isTaskDataLoaded" class="dispaly-center"><span>No Tasks found</span>
  </div>
  <ion-card class="header-card-details"
    *ngFor="let item of taskList | paginate: { itemsPerPage: itemsPerPage, currentPage: cp }">
    <ion-grid style="margin-bottom: -10px !important;margin-top: 5px;">
      <ion-row>
        <ion-col size="1" style="padding-left: 25px;"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <ion-icon name="hand-right" color="warning"
            *ngIf="item && item.TASK_TYPE && item.TASK_TYPE.includes('Clean')"></ion-icon>
          <ion-icon name="eye" color="primary" *ngIf="item && item.TASK_TYPE && item.TASK_TYPE.includes('Inspect')">
          </ion-icon>
          <ion-icon name="color-fill" color="danger" *ngIf="item && item.TASK_TYPE && item.TASK_TYPE.includes('Lube')">
          </ion-icon>
          <ion-icon name="build" color="danger" *ngIf="item && item.TASK_TYPE && item.TASK_TYPE.includes('Tighten')">
          </ion-icon>

        </ion-col>

        <ion-col size="1" *ngIf="selectedDate !== 'Others'"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.OPERATION_NO}}</p>
        </ion-col>

        <ion-col size="3" *ngIf="selectedDate !== 'Others'"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.OPERATION_DESC}}</p>
        </ion-col>

        <ion-col size="1" *ngIf="selectedDate === 'Others'"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.OPERATION_NO}}</p>
        </ion-col>

        <ion-col size="2" *ngIf="selectedDate === 'Others'"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.OPERATION_DESC}}</p>
        </ion-col>

        <ion-col size="1.25" *ngIf="selectedDate === 'Others'"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.BASIC_START_DATE | dateformat}}</p>
        </ion-col>

        <ion-col size="0.75"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <ion-button *ngIf="item.SYSTEM_CONDITION === 'X'" color="success"
            style="pointer-events:none !important;width: 70px !important;">RUN
          </ion-button>
          <ion-button *ngIf="item.SYSTEM_CONDITION !== 'X'" color="danger"
            style="pointer-events:none !important;width: 70px !important;">STOP
          </ion-button>
        </ion-col>

        <ion-col size="1" class="shift-style"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color">{{item.SHIFT}}</p>
        </ion-col>

        <ion-col size="2"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <ion-radio-group [(ngModel)]="item.COMPLETION_STATUS" *ngIf="item.COMPLETION_STATUS !== 'X'"
            (ionChange)="setCompletion($event, item)">
            <ion-radio slot="start" color="primary" value="true" style="margin-bottom: 15px;"
              (ionSelect)="clickev($event)"></ion-radio>
            <ion-icon name="checkmark-circle" color="success"></ion-icon>
            <ion-radio slot="start" color="primary" value="false" style="margin-bottom: 15px;"></ion-radio>
            <ion-icon name="close-circle" color="danger"></ion-icon>
          </ion-radio-group>

          <ion-radio-group [(ngModel)]="item.COMPLETION_STATUS"
            *ngIf="item.COMPLETION_STATUS === 'X' && item.REASON === ''" value="{{item.COMPLETION_STATUS}}">
            <ion-radio slot="start" value="{{item.COMPLETION_STATUS}}" color="primary" style="margin-bottom: 15px;">
            </ion-radio>
            <ion-icon name="checkmark-circle" color="success"></ion-icon>
          </ion-radio-group>

          <ion-radio-group [(ngModel)]="item.COMPLETION_STATUS" value="{{item.COMPLETION_STATUS}}"
            *ngIf="item.COMPLETION_STATUS === 'X' && item.REASON && item.REASON.length > 0">
            <ion-radio slot="start" color="primary" value="{{item.COMPLETION_STATUS}}"
              style="margin-bottom: 15px;"></ion-radio>
            <ion-icon name="close-circle" color="danger"></ion-icon>
          </ion-radio-group>
        </ion-col>

        <ion-col size="2"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <select style="border-radius: 5px;height: 70%;" [ngClass]="{'row-disable': item.COMPLETION_STATUS === 'X'}"
            class="text-color" [(ngModel)]="item.REASON"
            *ngIf="item.COMPLETION_STATUS === 'false' || (item.REASON && item.REASON.length > 0)" required
            value="{{item.REASON}}">
            <option disabled value="" selected>Select</option>
            <option *ngFor="let reason of reasonCodeList" value="{{reason.CODE}}">
              {{reason.DESCRP}}</option>
          </select>
        </ion-col>

        <ion-col size="1" style="pointer-events:auto !important;"
          [ngClass]="{ 'row-priority': (item.PRIORITY === 'X' && item.COMPLETION_STATUS !== 'X'), 'row-disable': item.COMPLETION_STATUS === 'X', 'row-enable': item.COMPLETION_STATUS !== 'X' }">
          <p class="text-color" *ngIf="item.URL"><a href="{{item.URL}}" target="_blank">{{item.OPERATION_NO}}</a></p>
        </ion-col>
      </ion-row>
    </ion-grid>
    <span *ngIf="item && item.errMsg && item.errMsg.length > 0" class="errMsg">{{item.errMsg}}</span>
  </ion-card> -->
</ion-content>

<ion-footer *ngIf="taskList && taskList.length > 0">
  <ion-toolbar>
    <!-- <pagination-controls (pageChange)="cp = $event"></pagination-controls> -->


    <div style="display: flex;margin: 10px;">
      <ion-checkbox color="primary" (ionChange)="checkMarkComplete($event)" [(ngModel)]="isMarkComplete"></ion-checkbox>
      <ion-label style="margin-left: 10px;">Mark All remaining tasks as COMPLETE.</ion-label>
    </div>
    <div style="display: flex;margin: 10px;">
      <ion-checkbox color="primary" (ionChange)="checkMarkInComplete($event)" [(ngModel)]="isMarkInComplete">
      </ion-checkbox>
      <ion-label style="margin-left: 10px;">Mark All remaining tasks as INCOMPLETE.</ion-label>
      <select style="border-radius: 5px;margin-left: 10px;height: 30px;margin-top: -5px;" class="text-color"
        *ngIf="isMarkInComplete" [(ngModel)]="inCompleteReason" (change)="selectedReason()">
        <option disabled value="" selected>Select</option>
        <option *ngFor="let item of reasonCodeList" value="{{item.CODE}}">
          {{item.DESCRP}}</option>
      </select>
    </div>
  </ion-toolbar>
</ion-footer>

<ion-footer *ngIf="taskList && taskList.length > 0">
  <ion-toolbar class="toolbar-bg">
    <!-- <ion-buttons slot="end"> -->
    <ion-button slot="end" (click)="updateTaskList()" class="footer-btn">Submit
    </ion-button>
    <ion-button slot="end" (click)="cancel()" class="footer-btn" style="margin-right: 20px !important;">Cancel
    </ion-button>
    <!-- </ion-buttons> -->
  </ion-toolbar>
</ion-footer>

<!-- <div class="container">
	<div class="row">
	    <form class="col-md-4">
	        <label>Select</label>
	        <select class="funloc-select" name="state">
	           <option>Select</option> 
	           <option>Car</option> 
	           <option>Bike</option> 
	           <option>Scooter</option> 
	           <option>Cycle</option> 
	           <option>Horse</option> 
	        </select>
	    </form>
 	</div>
</div> -->

<!-- <div style="text-align:center">
  <label>Select</label>
  <select class="funloc-select" name="state" style="width: 150px;">
    <option>Select</option> 
    <option>Car</option> 
    <option>Bike</option> 
    <option>Scooter</option> 
    <option>Cycle</option> 
    <option>Horse</option> 
</select>
</div> -->